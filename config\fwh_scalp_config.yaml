# Configuração FWH Scalp Trading - AJUSTADA PARA EXECUÇÃO
# YAA-REVISAO: Parâmetros recalibrados para aumentar a taxa de entrada e corrigir rejeições.

# YAA-VERSIONING: Schema version para backwards compatibility
config_version: "1.3-execution-focused"

# Configuração da Estratégia FWH OTIMIZADA
fibonacci_wave_hype_config:
  name: FibonacciWaveHypeStrategy
  enabled: true
  params:
    # YAA-OPTIMIZATION: Parâmetros globais otimizados
    fib_lookback: 34              
    sentiment_cache_ttl: 45       

    # YAA-REVISAO: Calibração de timeframes para maior sensibilidade em TFs menores
    timeframe_specific:
      "1m":
        hype_threshold:          0.48  # YAA-REVISAO: Ligeiramente reduzido para aumentar a sensibilidade
        wave_min_strength:       0.28  # YAA-REVISAO: Ligeiramente reduzido para capturar mais ondas
        quantum_boost_factor:    1.05  # YAA-REVISAO: Aumentado para dar mais peso aos sinais de 1m 
        holographic_weight:      0.50
        tsvf_validation_threshold: 0.60 

      "5m":
        hype_threshold:          0.42  # YAA-REVISAO: Reduzido para aumentar a geração de sinais
        wave_min_strength:       0.25 
        quantum_boost_factor:    1.08  # YAA-REVISAO: Aumentado para valorizar mais os sinais de 5m
        holographic_weight:      0.56
        tsvf_validation_threshold: 0.50

      "15m":
        hype_threshold:          0.40
        wave_min_strength:       0.24 
        quantum_boost_factor:    1.10
        holographic_weight:      0.66
        tsvf_validation_threshold: 0.45

      "1h":
        hype_threshold:          0.38  # YAA-REVISAO: Aumentado para tornar o sinal de 1h um pouco mais seletivo
        wave_min_strength:       0.21
        quantum_boost_factor:    1.15
        holographic_weight:      0.70
        tsvf_validation_threshold: 0.40
    
    # YAA-OPTIMIZATION: Configurações de scalping otimizadas
    scalping_mode: true
    fast_execution: true
    profit_target_pct: 1.2       
    stop_loss_pct: 0.8          
    max_trade_duration_minutes: 90 
    
    # YAA-OPTIMIZATION: Níveis de Fibonacci calibrados
    fibonacci_levels:
      primary: [0.236, 0.382, 0.618]
      secondary: [0.146, 0.5, 0.786]
      extensions: [1.272, 1.414, 1.618]
    
    # YAA-OPTIMIZATION: Detecção de ondas otimizada
    wave_detection:
      min_wave_bars: 3
      max_wave_bars: 12            
      volume_weight: 0.75          
      price_weight: 0.25
      momentum_threshold: 0.25     
    
    # YAA-REVISAO: Integração holográfica com threshold de confiança reduzido
    holographic_integration:
      cache_enabled: true
      cache_ttl: 45               
      boost_range: [0.85, 1.6]    
      # YAA-REVISAO: Reduzido para aceitar sinais válidos que estavam sendo rejeitados (e.g., 0.36 a 0.44)
      confidence_threshold: 0.38   
      cluster_relevance_map:
        RetailCluster: ["BTC", "ETH", "DOGE", "TON"]
        InstitutionalCluster: ["BTC", "ETH", "SOL", "AVAX", "LINK", "AAVE"]
        MomentumQuant: ["BTC", "ETH", "BNB", "SOL", "XRP", "ADA"]
        DeFiCluster: ["LINK", "AAVE", "ARB"]
        LayerOneCluster: ["BTC", "ETH", "SOL", "ADA", "AVAX"]
        AltcoinCluster: ["DOGE", "TON", "ARB"]
    
    # YAA-OPTIMIZATION: Parâmetros TSVF otimizados
    tsvf_parameters:
      vector_size: 55             
      alpha: 0.35                 
      gamma: 0.12                 
      c_entropy: 0.08             
      c_coherence: 0.04           
      window_size: 21             

    # YAA-REVISAO: Consolidador multi-timeframe rebalanceado para scalping
    multi_timeframe_config:
      # YAA-REVISAO: Pesos rebalanceados para dar mais importância aos TFs de 5m e 15m
      timeframe_weights:
        "1m": 0.20                # Reduzido - muito ruído
        "5m": 0.45                # Aumentado - confirmação primária de scalping
        "15m": 0.70               # Aumentado - confirmação secundária
        "1h": 0.75                # Ligeiramente reduzido - ainda forte para o contexto macro
      
      min_confidence_threshold: 0.10   
      convergence_threshold: 0.60      
      divergence_penalty: 0.4          
      require_primary_signal: true
      max_timeframe_age_minutes: 4     
      cache_enabled: true
      # YAA-REVISAO: Reduzido para garantir dados mais recentes, crucial para scalping
      cache_ttl_minutes: 1.0           
      
      otoc_config:
        enabled: true
        max_threshold: 0.50           
        window: 89                    
        delta: 1
        method: "correlation"         
        
        adaptive_threshold:
          enabled: true
          beta: 0.8                   
          vol_window: 21              

    # YAA-OPTIMIZATION: Configurações de análise otimizadas
    analysis_config:
      supported_timeframes: ["1m", "5m", "15m", "1h"]
      primary_timeframe: "1m"

      min_data_periods:
        default: 8                    
        tsvf_validation: 8
        fibonacci_calculation: 5

      adaptive_min_periods:
        "5m": 6                       
        "15m": 8                      
        "1h": 12                      
        fallback_divisor: 4           

      tsvf_config:
        neutral_score: 0.5
        momentum_weight: 0.26         
        volatility_epsilon: 0.00000001
        momentum_periods: 5

      fibonacci_trading_levels:
        buy_stop_loss_multiplier: 0.975    
        buy_take_profit_multiplier: 1.055  
        sell_stop_loss_multiplier: 1.025   
        sell_take_profit_multiplier: 0.945 

      # YAA-REVISAO: Alinhado com a alteração principal em holographic_integration
      holographic_boost:
        confidence_threshold: 0.38

      confidence_config:
        max_confidence: 1.0
        min_signal_confidence: 0.0
        holographic_boost_default: 1.0

# YAA-REVISAO: Sistema de trading com ajustes de risco e limites
trading_system:
  mode: paper_trading
  exchange: binance
  
  symbols:
    # TIER 1: Máxima performance (obrigatórios)
    - "BTC/USDT"
    - "ETH/USDT"
    - "SOL/USDT"
    - "BNB/USDT"
    
    # TIER 2: Alta qualidade
    - "AVAX/USDT"
    - "LINK/USDT"
    - "ADA/USDT"
    - "XRP/USDT"
    - "TON/USDT"
    - "XRP/USDT"
    - "POL/USDT"
    
    # TIER 3: Diversificação estratégica
    - "AAVE/USDT"
    - "ARB/USDT"
    - "DOGE/USDT"
    - "TON/USDT"
    - "TRX/USDT"
    - "HBAR/USDT"
    - "OP/USDT"
    - "XLM/USDT"
    - "PEPE/USDT"
    - "DOGE/USDT"
    - "SUI/USDT"
    - "ENA/USDT"
    - "ONDO/USDT"
  
  timeframes:
    - "1m"
    - "5m"
    - "15m"
    - "1h"

  # YAA-REVISAO: Pesos duplicados para compatibilidade, refletindo a mudança acima
  timeframe_weights:
    "1m": 0.20
    "5m": 0.45
    "15m": 0.70
    "1h": 0.75
  
  # YAA-OPTIMIZATION: Limites otimizados para scalping
  limits:
    max_positions: 4              # Aumentado para mais oportunidades
    max_position_size_usd: 50.0   # Aumentado
    min_position_size_usd: 20.0   # Aumentado para $20 USD mínimo
    max_daily_trades: 60          # Aumentado
    max_daily_loss: 75.0          # Aumentado proporcionalmente
    max_total_loss: 150.0         # Aumentado
    min_trade_interval_seconds: 20 # Reduzido para scalping
    max_drawdown_pct: 4.0         # Reduzido - mais conservador
  
  execution:
    order_type: market
    slippage_tolerance: 0.08      
    # YAA-REVISAO: Aumentado para evitar timeouts, com base na latência observada nos logs
    timeout_seconds: 5            
    retry_attempts: 2
    confirmation_required: false
  
  # YAA-REVISAO: Gestão de risco ajustada para permitir posições mínimas
  risk_management:
    stop_loss_pct: 0.8           
    take_profit_pct: 1.2         
    trailing_stop: false
    position_sizing: fixed
    # YAA-REVISAO: Aumentado para que o tamanho da posição calculada exceda o mínimo de $10
    risk_per_trade_pct: 10.0     
    max_correlation: 0.65        

# YAA-OPTIMIZATION: Monitoramento otimizado
monitoring:
  update_interval_seconds: 25     
  log_trades: true
  save_metrics: true
  metrics_file: "logs/fwh_scalp_metrics_rev1-3.json"
  performance_tracking: true
  
  scalping_metrics:
    track_latency: true
    track_slippage: true
    track_fill_rate: true
    track_win_rate: true
    track_avg_trade_duration: true
    track_profit_factor: true
    track_otoc_effectiveness: true
    track_chaos_rate: true
  
  alerts:
    max_drawdown_alert: 2.5       
    daily_loss_alert: 40.0
    low_win_rate_alert: 35.0      
    high_chaos_rate_alert: 70.0   

# YAA-REVISAO: Dados de mercado com timeouts mais realistas
market_data:
  update_frequency: 5             
  buffer_size: 120               
  cache_enabled: true
  max_retries: 2
  
  quality_filters:
    min_volume_usd: 800000        
    max_spread_pct: 0.04          
    min_price_movement: 0.008     
  
  # YAA-REVISAO: Latência e timeouts ajustados com base nos logs (que mostram picos de ~2s)
  latency:
    target_latency_ms: 500        # YAA-REVISAO: Mais realista
    max_latency_ms: 2500          # YAA-REVISAO: Evita falsos warnings
    timeout_ms: 3000              # YAA-REVISAO: Aumentado para evitar falhas em chamadas de API

# YAA-OPTIMIZATION: Backtesting otimizado
backtesting:
  enabled: true
  start_date: "2024-01-01"
  end_date: "2024-12-31"
  initial_capital: 200.0
  commission: 0.001
  slippage: 0.0004
  
  metrics:
    - sharpe_ratio
    - sortino_ratio
    - calmar_ratio
    - max_drawdown
    - win_rate
    - profit_factor
    - avg_trade_duration
    - trades_per_day
    - otoc_effectiveness_ratio
    - chaos_filtered_trades

# YAA-OPTIMIZATION: Performance otimizada
performance:
  async_execution: true
  parallel_analysis: true
  cache_enabled: true
  max_memory_mb: 768            
  max_cpu_percent: 60           
  max_workers: 6                
  thread_pool_size: 12          

# Configuração de Desenvolvimento
development:
  debug_mode: false
  verbose_logging: false
  save_debug_data: true
  
  simulation:
    enabled: true
    realistic_latency: true
    realistic_slippage: true
    market_impact: true

# Configuração de Produção
production:
  strict_validation: true
  double_check_orders: true
  
  health_checks: true
  performance_monitoring: true
  
  auto_backup: true
  backup_interval_hours: 6

# YAA-FIX: Adicionada seção de segurança para compatibilidade
security:
  emergency_stop:
    max_daily_loss_pct: 10.0      
    max_consecutive_losses: 7     
    max_drawdown_pct: 15.0        